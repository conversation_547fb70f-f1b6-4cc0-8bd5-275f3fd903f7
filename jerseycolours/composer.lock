{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "d5bace60723dc717cfceff6f4e435aa4", "packages": [{"name": "composer/ca-bundle", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d665d22c417056996c59019579f1967dfe5c1e82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82", "reference": "d665d22c417056996c59019579f1967dfe5c1e82", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-26T15:08:54+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/data-fixtures", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "f18adf13f6c81c67a88360dca359ad474523f8e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/f18adf13f6c81c67a88360dca359ad474523f8e3", "reference": "f18adf13f6c81c67a88360dca359ad474523f8e3", "shasum": ""}, "require": {"doctrine/common": "^2.13|^3.0", "doctrine/persistence": "^1.3.3|^2.0", "php": "^7.2 || ^8.0"}, "conflict": {"doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "doctrine/dbal": "^2.5.4 || ^3.0", "doctrine/mongodb-odm": "^1.3.0 || ^2.0.0", "doctrine/orm": "^2.7.0", "ext-sqlite3": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM 1.3 with PHP 7 (deprecated)", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "lib/Doctrine/Common/DataFixtures"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "http://www.doctrine-project.org", "keywords": ["database"], "support": {"issues": "https://github.com/doctrine/data-fixtures/issues", "source": "https://github.com/doctrine/data-fixtures/tree/1.5.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdata-fixtures", "type": "tidelift"}], "time": "2021-09-20T21:51:43+00:00"}, {"name": "doctrine/dbal", "version": "v2.4.5", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "5a1f4bf34d61d997ccd9f0539fbc14c7a772aa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/5a1f4bf34d61d997ccd9f0539fbc14c7a772aa16", "reference": "5a1f4bf34d61d997ccd9f0539fbc14c7a772aa16", "shasum": ""}, "require": {"doctrine/common": "~2.4", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "3.7.*", "symfony/console": "~2.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "type": "library", "autoload": {"psr-0": {"Doctrine\\DBAL\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Database Abstraction Layer", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "persistence", "queryobject"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/v2.4.5"}, "time": "2016-01-05T22:18:20+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.6.13", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "8cd4c2921b6cef14a78d98cd3f0fb81ba6a976f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/8cd4c2921b6cef14a78d98cd3f0fb81ba6a976f9", "reference": "8cd4c2921b6cef14a78d98cd3f0fb81ba6a976f9", "shasum": ""}, "require": {"doctrine/dbal": "~2.3", "doctrine/doctrine-cache-bundle": "~1.2", "jdorn/sql-formatter": "~1.1", "php": ">=5.5.9", "symfony/console": "~2.7|~3.0|~4.0", "symfony/dependency-injection": "~2.7|~3.0|~4.0", "symfony/doctrine-bridge": "~2.7|~3.0|~4.0", "symfony/framework-bundle": "~2.7|~3.0|~4.0"}, "require-dev": {"doctrine/orm": "~2.3", "phpunit/phpunit": "~4", "satooshi/php-coveralls": "^1.0", "symfony/phpunit-bridge": "~2.7|~3.0|~4.0", "symfony/property-info": "~2.8|~3.0|~4.0", "symfony/validator": "~2.7|~3.0|~4.0", "symfony/yaml": "~2.7|~3.0|~4.0", "twig/twig": "~1.12|~2.0"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/1.6.x"}, "time": "2017-10-11T19:48:03+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.3.5", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "5514c90d9fb595e1095e6d66ebb98ce9ef049927"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/5514c90d9fb595e1095e6d66ebb98ce9ef049927", "reference": "5514c90d9fb595e1095e6d66ebb98ce9ef049927", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "~1.0", "php": ">=5.3.2", "symfony/doctrine-bridge": "~2.7|~3.3|~4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "~4.8.36|~5.6|~6.5|~7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "~2.7|~3.3|~4.0", "symfony/finder": "~2.7|~3.3|~4.0", "symfony/framework-bundle": "~2.7|~3.3|~4.0", "symfony/phpunit-bridge": "~2.7|~3.3|~4.0", "symfony/security-acl": "~2.7|~3.3", "symfony/validator": "~2.7|~3.3|~4.0", "symfony/yaml": "~2.7|~3.3|~4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "support": {"issues": "https://github.com/doctrine/DoctrineCacheBundle/issues", "source": "https://github.com/doctrine/DoctrineCacheBundle/tree/1.3.5"}, "abandoned": true, "time": "2018-11-09T06:25:35+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "v2.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "74b8cc70a4a25b774628ee59f4cdf3623a146273"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/74b8cc70a4a25b774628ee59f4cdf3623a146273", "reference": "74b8cc70a4a25b774628ee59f4cdf3623a146273", "shasum": ""}, "require": {"doctrine/data-fixtures": "~1.0", "doctrine/doctrine-bundle": "~1.0", "php": ">=5.3.2", "symfony/doctrine-bridge": "~2.7|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineFixturesBundle/issues", "source": "https://github.com/doctrine/DoctrineFixturesBundle/tree/master"}, "time": "2017-10-30T19:26:42+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/orm", "version": "v2.4.8", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "5aedac1e5c5caaeac14798822c70325dc242d467"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/5aedac1e5c5caaeac14798822c70325dc242d467", "reference": "5aedac1e5c5caaeac14798822c70325dc242d467", "shasum": ""}, "require": {"doctrine/collections": "~1.1", "doctrine/dbal": "~2.4", "ext-pdo": "*", "php": ">=5.3.2", "symfony/console": "~2.0"}, "require-dev": {"satooshi/php-coveralls": "dev-master", "symfony/yaml": "~2.1"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine", "bin/doctrine.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\ORM\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/v2.4.8"}, "time": "2015-08-31T13:19:01+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "shasum": ""}, "require": {"doctrine/annotations": "^1.0 || ^2.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.4"}, "abandoned": "roave/better-reflection", "time": "2023-07-27T18:11:59+00:00"}, {"name": "fredcido/phpsvg", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/fredcido/phpsvg.git", "reference": "c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fredcido/phpsvg/zipball/c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd", "reference": "c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd", "shasum": ""}, "default-branch": true, "type": "library", "autoload": {"psr-0": {"Fredcido\\PHPSVG": "src"}}, "support": {"source": "https://github.com/fredcido/phpsvg/tree/master", "issues": "https://github.com/fredcido/phpsvg/issues"}, "time": "2015-08-30T14:11:45+00:00"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "70ed446888cadf49312e12b42b80adbb395a2d3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/70ed446888cadf49312e12b42b80adbb395a2d3f", "reference": "70ed446888cadf49312e12b42b80adbb395a2d3f", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/console": "~2.3||~3.0", "symfony/framework-bundle": "~2.3||~3.0", "symfony/serializer": "~2.3||~3.0", "willdurand/jsonp-callback-validator": "~1.0"}, "require-dev": {"symfony/expression-language": "~2.4||~3.0", "symfony/phpunit-bridge": "^3.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/tree/master"}, "time": "2017-11-08T21:18:03+00:00"}, {"name": "friendsofsymfony/rest-bundle", "version": "1.7.3", "target-dir": "FOS/RestBundle", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSRestBundle.git", "reference": "a96e829fc3f503675bebea2d0d8e170213ab210a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSRestBundle/zipball/a96e829fc3f503675bebea2d0d8e170213ab210a", "reference": "a96e829fc3f503675bebea2d0d8e170213ab210a", "shasum": ""}, "require": {"doctrine/inflector": "~1.0", "php": ">=5.3.9", "psr/log": "~1.0", "symfony/framework-bundle": "~2.3|~3.0", "symfony/http-kernel": "^2.3.24|~3.0", "willdurand/jsonp-callback-validator": "~1.0", "willdurand/negotiation": "~1.2"}, "conflict": {"jms/serializer": "<0.12", "jms/serializer-bundle": "<0.11", "symfony/validator": ">=2.5.0,<2.5.5"}, "require-dev": {"jms/serializer": "~0.13|~1.0", "jms/serializer-bundle": "~0.12|~1.0", "phpoption/phpoption": "~1.1.0", "sensio/framework-extra-bundle": "~2.0|~3.0", "sllh/php-cs-fixer-styleci-bridge": "^1.3", "symfony/browser-kit": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/form": "~2.3|~3.0", "symfony/phpunit-bridge": "~2.7|~3.0", "symfony/security": "~2.3|~3.0", "symfony/serializer": "~2.3|~3.0", "symfony/validator": "~2.3|~3.0", "symfony/yaml": "~2.3|~3.0"}, "suggest": {"jms/serializer-bundle": "Add support for advanced serialization capabilities, recommended, requires ~0.12||~1.0", "sensio/framework-extra-bundle": "Add support for route annotations and the view response listener, requires ~3.0", "symfony/serializer": "Add support for basic serialization capabilities and xml decoding, requires ~2.3", "symfony/validator": "Add support for validation capabilities in the ParamFetcher, requires ~2.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-0": {"FOS\\RestBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSRestBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "This Bundle provides various tools to rapidly develop RESTful API's with Symfony2", "homepage": "http://friendsofsymfony.github.com", "keywords": ["rest"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSRestBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSRestBundle/tree/1.7"}, "time": "2015-12-04T21:34:34+00:00"}, {"name": "friendsofsymfony/user-bundle", "version": "v1.3.7", "target-dir": "FOS/UserBundle", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSUserBundle.git", "reference": "113cc4e48fac19d20fc039cfecae9b41103be706"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSUserBundle/zipball/113cc4e48fac19d20fc039cfecae9b41103be706", "reference": "113cc4e48fac19d20fc039cfecae9b41103be706", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/framework-bundle": "~2.3", "symfony/security-bundle": "~2.3"}, "require-dev": {"doctrine/doctrine-bundle": "~1.3", "swiftmailer/swiftmailer": "~4.3|~5", "symfony/validator": "~2.3", "symfony/yaml": "~2.3", "twig/twig": "~1.5", "willdurand/propel-typehintable-behavior": "~1.0"}, "suggest": {"willdurand/propel-typehintable-behavior": "Needed when using the propel implementation"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"FOS\\UserBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSUserBundle/contributors"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony FOSUserBundle", "homepage": "http://friendsofsymfony.github.com", "keywords": ["User management"], "support": {"docs": "https://symfony.com/doc/1.3.x/bundles/FOSUserBundle/index.html", "issues": "https://github.com/FriendsOfSymfony/FOSUserBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSUserBundle/tree/1.3.x"}, "time": "2016-08-12T09:53:32+00:00"}, {"name": "glanchow/doctrine-fuzzy", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/glanchow/doctrine-fuzzy.git", "reference": "b093f6b3cf258abab3f98976bf728bd210fe1845"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glanchow/doctrine-fuzzy/zipball/b093f6b3cf258abab3f98976bf728bd210fe1845", "reference": "b093f6b3cf258abab3f98976bf728bd210fe1845", "shasum": ""}, "require": {"doctrine/orm": ">=2.2.0"}, "default-branch": true, "type": "library", "autoload": {"psr-0": {"WOK": "lib/"}}, "license": ["MIT"], "description": "Doctrine2 DQL fuzzy functions", "keywords": ["against", "doctrine", "<PERSON><PERSON><PERSON><PERSON>", "match", "soundex", "symfony"], "support": {"source": "https://github.com/glanchow/doctrine-fuzzy/tree/master", "issues": "https://github.com/glanchow/doctrine-fuzzy/issues"}, "time": "2014-11-08T12:07:09+00:00"}, {"name": "guzzle/guzzle", "version": "v3.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle3.git", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle3/zipball/0645b70d953bc1c067bbc8d5bc53194706b628d9", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.3", "symfony/event-dispatcher": "~2.1"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}, "require-dev": {"doctrine/cache": "~1.3", "monolog/monolog": "~1.0", "phpunit/phpunit": "3.7.*", "psr/log": "~1.0", "symfony/class-loader": "~2.1", "zendframework/zend-cache": "2.*,<2.3", "zendframework/zend-log": "2.*,<2.3"}, "suggest": {"guzzlehttp/guzzle": "Guzzle 5 has moved to a new package name. The package you have installed, Guzzle 3, is deprecated."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "description": "PHP HTTP client. This library is deprecated in favor of https://packagist.org/packages/guzzlehttp/guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle3/issues", "source": "https://github.com/guzzle/guzzle3/tree/master"}, "abandoned": "guzzlehttp/guzzle", "time": "2015-03-18T18:23:50+00:00"}, {"name": "incenteev/composer-parameter-handler", "version": "v2.1.5", "source": {"type": "git", "url": "https://github.com/Incenteev/ParameterHandler.git", "reference": "e1dd118763503f7fd766f907013e1d76d525fcc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Incenteev/ParameterHandler/zipball/e1dd118763503f7fd766f907013e1d76d525fcc4", "reference": "e1dd118763503f7fd766f907013e1d76d525fcc4", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/yaml": "^2.3 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"composer/composer": "^1.0@dev", "symfony/filesystem": "^2.3 || ^3 || ^4 || ^5 || ^6.0", "symfony/phpunit-bridge": "^3.4.47 || ^4.4.41 || ^5.4.8 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Incenteev\\ParameterHandler\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer script handling your ignored parameter file", "homepage": "https://github.com/Incenteev/ParameterHandler", "keywords": ["parameters management"], "support": {"issues": "https://github.com/Incenteev/ParameterHandler/issues", "source": "https://github.com/Incenteev/ParameterHandler/tree/v2.1.5"}, "time": "2022-05-25T10:57:22+00:00"}, {"name": "jakoch/phantomjs-installer", "version": "1.9.8", "source": {"type": "git", "url": "https://github.com/jakoch/phantomjs-installer.git", "reference": "fd6459094404f698a4edb9609e5e8c55c6418824"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakoch/phantomjs-installer/zipball/fd6459094404f698a4edb9609e5e8c55c6418824", "reference": "fd6459094404f698a4edb9609e5e8c55c6418824", "shasum": ""}, "require": {"ext-openssl": "*"}, "type": "custom-installer", "autoload": {"psr-0": {"PhantomInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A Composer package which installs the PhantomJS binary (Linux, Windows, Mac) into `/bin` of your project.", "keywords": ["binaries", "headless", "phantom<PERSON>s"], "support": {"issues": "https://github.com/jakoch/phantomjs-installer/issues", "source": "https://github.com/jakoch/phantomjs-installer/tree/master"}, "time": "2014-12-05T17:57:27+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/jdorn/sql-formatter/issues", "source": "https://github.com/jdorn/sql-formatter/tree/v1.2.17"}, "time": "2014-01-12T16:20:24+00:00"}, {"name": "jms/metadata", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "e5854ab1aa643623dc64adde718a8eec32b957a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/e5854ab1aa643623dc64adde718a8eec32b957a8", "reference": "e5854ab1aa643623dc64adde718a8eec32b957a8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"doctrine/cache": "~1.0", "symfony/cache": "~3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "support": {"issues": "https://github.com/schmittjoh/metadata/issues", "source": "https://github.com/schmittjoh/metadata/tree/1.x"}, "time": "2018-10-26T12:40:10+00:00"}, {"name": "jms/parser-lib", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/parser-lib.git", "reference": "4f45952f9fa97d67adc5dd69e7d622fc89a7675d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/parser-lib/zipball/4f45952f9fa97d67adc5dd69e7d622fc89a7675d", "reference": "4f45952f9fa97d67adc5dd69e7d622fc89a7675d", "shasum": ""}, "require": {"phpoption/phpoption": ">=0.9,<2.0-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-0": {"JMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "description": "A library for easily creating recursive-descent parsers.", "support": {"issues": "https://github.com/schmittjoh/parser-lib/issues", "source": "https://github.com/schmittjoh/parser-lib/tree/1.0.1"}, "time": "2022-03-19T09:24:56+00:00"}, {"name": "jms/serializer", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "62c7ff6d61f8692eac8be024c542b3d9d0ab8c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/62c7ff6d61f8692eac8be024c542b3d9d0ab8c8a", "reference": "62c7ff6d61f8692eac8be024c542b3d9d0ab8c8a", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/instantiator": "^1.0.3", "jms/metadata": "~1.1", "jms/parser-lib": "1.*", "php": ">=5.5.0", "phpcollection/phpcollection": "~0.1", "phpoption/phpoption": "^1.1"}, "conflict": {"jms/serializer-bundle": "<1.2.1", "twig/twig": "<1.12"}, "require-dev": {"doctrine/orm": "~2.1", "doctrine/phpcr-odm": "^1.3|^2.0", "ext-pdo_sqlite": "*", "jackalope/jackalope-doctrine-dbal": "^1.1.5", "phpunit/phpunit": "^4.8|^5.0", "propel/propel1": "~1.7", "psr/container": "^1.0", "symfony/dependency-injection": "^2.7|^3.3|^4.0", "symfony/expression-language": "^2.6|^3.0", "symfony/filesystem": "^2.1", "symfony/form": "~2.1|^3.0", "symfony/translation": "^2.1|^3.0", "symfony/validator": "^2.2|^3.0", "symfony/yaml": "^2.1|^3.0", "twig/twig": "~1.12|~2.0"}, "suggest": {"doctrine/cache": "Required if you like to use cache functionality.", "doctrine/collections": "Required if you like to use doctrine collection types as ArrayCollection.", "symfony/yaml": "Required if you'd like to serialize data to YAML format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-0": {"JMS\\Serializer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "support": {"issues": "https://github.com/schmittjoh/serializer/issues", "source": "https://github.com/schmittjoh/serializer/tree/master"}, "time": "2017-11-30T18:23:40+00:00"}, {"name": "jms/serializer-bundle", "version": "1.5.0", "target-dir": "JMS/SerializerBundle", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSSerializerBundle.git", "reference": "85ee039a2b7f89d77c403e33cee7b43a875c31e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSSerializerBundle/zipball/85ee039a2b7f89d77c403e33cee7b43a875c31e5", "reference": "85ee039a2b7f89d77c403e33cee7b43a875c31e5", "shasum": ""}, "require": {"jms/serializer": "^1.7", "php": ">=5.4.0", "phpoption/phpoption": "^1.1.0", "symfony/framework-bundle": "~2.3|~3.0"}, "require-dev": {"doctrine/doctrine-bundle": "*", "doctrine/orm": "*", "phpunit/phpunit": "^4.2|^5.0", "symfony/browser-kit": "*", "symfony/class-loader": "*", "symfony/css-selector": "*", "symfony/expression-language": "~2.6|~3.0", "symfony/finder": "*", "symfony/form": "*", "symfony/process": "*", "symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/validator": "*", "symfony/yaml": "*"}, "suggest": {"jms/di-extra-bundle": "Required to get lazy loading (de)serialization visitors, ~1.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"JMS\\SerializerBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Allows you to easily serialize, and deserialize data of any complexity", "homepage": "http://jmsyst.com/bundles/JMSSerializerBundle", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "support": {"issues": "https://github.com/schmittjoh/JMSSerializerBundle/issues", "source": "https://github.com/schmittjoh/JMSSerializerBundle/tree/master"}, "time": "2017-05-10T10:17:17+00:00"}, {"name": "knplabs/knp-components", "version": "v1.3.10", "source": {"type": "git", "url": "https://github.com/KnpLabs/knp-components.git", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/knp-components/zipball/fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "reference": "fc1755ba2b77f83a3d3c99e21f3026ba2a1429be", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"doctrine/mongodb-odm": "~1.0@beta", "doctrine/orm": "~2.4", "doctrine/phpcr-odm": "~1.2", "jackalope/jackalope-doctrine-dbal": "~1.2", "phpunit/phpunit": "~4.2", "ruflin/elastica": "~1.0", "symfony/event-dispatcher": "~2.5", "symfony/property-access": ">=2.3"}, "suggest": {"symfony/property-access": "To allow sorting arrays"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Knp\\Component": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/knp-components/contributors"}], "description": "Knplabs component library", "homepage": "http://github.com/KnpLabs/knp-components", "keywords": ["components", "knp", "knplabs", "pager", "paginator"], "support": {"issues": "https://github.com/KnpLabs/knp-components/issues", "source": "https://github.com/KnpLabs/knp-components/tree/master"}, "time": "2018-09-11T07:54:48+00:00"}, {"name": "knplabs/knp-menu", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenu.git", "reference": "56ffccc3ed8f7b69ec6e6d587e9f840c0a270739"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenu/zipball/56ffccc3ed8f7b69ec6e6d587e9f840c0a270739", "reference": "56ffccc3ed8f7b69ec6e6d587e9f840c0a270739", "shasum": ""}, "require": {"php": ">=5.6.0"}, "conflict": {"twig/twig": "<1.40|>=2,<2.9"}, "require-dev": {"psr/container": "^1.0", "symfony/http-foundation": "~2.4|~3.0|^4.0", "symfony/phpunit-bridge": "~3.3|^4.0", "symfony/routing": "~2.3|~3.0|^4.0", "twig/twig": "^1.40|^2.9"}, "suggest": {"twig/twig": "for the TwigRenderer and the integration with your templates"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Knp\\Menu\\": "src/Knp/Menu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs", "homepage": "https://knplabs.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://github.com/KnpLabs/KnpMenu/contributors"}], "description": "An object oriented menu library", "homepage": "https://knplabs.com", "keywords": ["menu", "tree"], "support": {"issues": "https://github.com/KnpLabs/KnpMenu/issues", "source": "https://github.com/KnpLabs/KnpMenu/tree/2.6.1"}, "time": "2021-02-19T07:45:31+00:00"}, {"name": "knplabs/knp-menu-bundle", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenuBundle.git", "reference": "0e4af7209dc03e39c51ec70b68ab2ba3177c25de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenuBundle/zipball/0e4af7209dc03e39c51ec70b68ab2ba3177c25de", "reference": "0e4af7209dc03e39c51ec70b68ab2ba3177c25de", "shasum": ""}, "require": {"knplabs/knp-menu": "~2.2", "symfony/framework-bundle": "~2.3|~3.0"}, "require-dev": {"symfony/expression-language": "~2.4|~3.0", "symfony/phpunit-bridge": "~2.7|~3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\MenuBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Knplabs", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "https://github.com/KnpLabs/KnpMenuBundle/contributors"}], "description": "This bundle provides an integration of the KnpMenu library", "keywords": ["menu"], "support": {"issues": "https://github.com/KnpLabs/KnpMenuBundle/issues", "source": "https://github.com/KnpLabs/KnpMenuBundle/tree/master"}, "time": "2016-09-22T12:24:40+00:00"}, {"name": "knplabs/knp-paginator-bundle", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpPaginatorBundle.git", "reference": "f4ece5b347121b9fe13166264f197f90252d4bd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpPaginatorBundle/zipball/f4ece5b347121b9fe13166264f197f90252d4bd2", "reference": "f4ece5b347121b9fe13166264f197f90252d4bd2", "shasum": ""}, "require": {"knplabs/knp-components": "~1.2", "php": ">=5.3.3", "symfony/framework-bundle": "~2.7|~3.0|~4.0", "twig/twig": "~1.12|~2"}, "require-dev": {"phpunit/phpunit": "~4.8.35|~5.4.3|~6.4", "symfony/expression-language": "~2.7|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\PaginatorBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle/contributors"}], "description": "Paginator bundle for Symfony to automate pagination and simplify sorting and other features", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle", "keywords": ["bundle", "knp", "knplabs", "pager", "pagination", "paginator", "symfony"], "support": {"issues": "https://github.com/KnpLabs/KnpPaginatorBundle/issues", "source": "https://github.com/KnpLabs/KnpPaginatorBundle/tree/master"}, "time": "2018-05-16T12:15:58+00:00"}, {"name": "knplabs/knp-snappy", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/KnpLabs/snappy.git", "reference": "144c4ecd1ccaeda936bf832b93079efc490e6850"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/snappy/zipball/144c4ecd1ccaeda936bf832b93079efc490e6850", "reference": "144c4ecd1ccaeda936bf832b93079efc490e6850", "shasum": ""}, "require": {"php": ">=5.6", "psr/log": "^1.0", "symfony/process": "~2.3 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "suggest": {"h4cc/wkhtmltoimage-amd64": "Provides wkhtmltoimage-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltoimage-i386": "Provides wkhtmltoimage-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-amd64": "Provides wkhtmltopdf-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-i386": "Provides wkhtmltopdf-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "wemersonjanuario/wkhtmltopdf-windows": "Provides wkhtmltopdf executable for Windows, use version `~0.12` as dependency"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Knp\\Snappy\\": "src/Knp/Snappy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/snappy/contributors"}], "description": "PHP5 library allowing thumbnail, snapshot or PDF generation from a url or a html page. Wrapper for wkhtmltopdf/wkhtmltoimage.", "homepage": "http://github.com/KnpLabs/snappy", "keywords": ["knp", "knplabs", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "support": {"issues": "https://github.com/KnpLabs/snappy/issues", "source": "https://github.com/KnpLabs/snappy/tree/master"}, "time": "2018-01-22T19:40:51+00:00"}, {"name": "knplabs/knp-snappy-bundle", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpSnappyBundle.git", "reference": "87acf8544a2bd498585b3d49b2bb551f088551b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpSnappyBundle/zipball/87acf8544a2bd498585b3d49b2bb551f088551b1", "reference": "87acf8544a2bd498585b3d49b2bb551f088551b1", "shasum": ""}, "require": {"knplabs/knp-snappy": "~1.0,>=1.0.1", "php": ">=7.1", "symfony/framework-bundle": "~2.7|~3.0|^4.0"}, "require-dev": {"doctrine/annotations": "~1.0", "phpunit/phpunit": "~7.4", "symfony/asset": "~2.7|~3.0|^4.0", "symfony/finder": "~2.7|~3.0|^4.0", "symfony/security-csrf": "~2.7|~3.0|^4.0", "symfony/templating": "~2.7|~3.0|^4.0", "symfony/validator": "~2.7|~3.0|^4.0", "symfony/yaml": "~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\SnappyBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle/contributors"}], "description": "Easily create PDF and images in Symfony2 by converting Twig/HTML templates.", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle", "keywords": ["bundle", "knp", "knplabs", "pdf", "snappy"], "support": {"issues": "https://github.com/KnpLabs/KnpSnappyBundle/issues", "source": "https://github.com/KnpLabs/KnpSnappyBundle/tree/v1.6.0"}, "time": "2018-12-13T12:14:08+00:00"}, {"name": "kriswallsmith/assetic", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/kriswallsmith/assetic.git", "reference": "e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kriswallsmith/assetic/zipball/e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1", "reference": "e911c437dbdf006a8f62c2f59b15b2d69a5e0aa1", "shasum": ""}, "require": {"php": ">=5.3.1", "symfony/process": "~2.1|~3.0"}, "conflict": {"twig/twig": "<1.27"}, "require-dev": {"leafo/lessphp": "^0.3.7", "leafo/scssphp": "~0.1", "meenie/javascript-packer": "^1.1", "mrclay/minify": "<2.3", "natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "~1.0|~2.0", "phpunit/phpunit": "~4.8 || ^5.6", "psr/log": "~1.0", "ptachoire/cssembed": "~1.0", "symfony/phpunit-bridge": "~2.7|~3.0", "twig/twig": "~1.23|~2.0", "yfix/packager": "dev-master"}, "suggest": {"leafo/lessphp": "Assetic provides the integration with the lessphp LESS compiler", "leafo/scssphp": "Assetic provides the integration with the scssphp SCSS compiler", "leafo/scssphp-compass": "Assetic provides the integration with the SCSS compass plugin", "patchwork/jsqueeze": "Assetic provides the integration with the JSqueeze JavaScript compressor", "ptachoire/cssembed": "Assetic provides the integration with phpcssembed to embed data uris", "twig/twig": "Assetic provides the integration with the Twig templating engine"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-0": {"Assetic": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Asset Management for PHP", "homepage": "https://github.com/kriswallsmith/assetic", "keywords": ["assets", "compression", "minification"], "support": {"issues": "https://github.com/kriswallsmith/assetic/issues", "source": "https://github.com/kriswallsmith/assetic/tree/master"}, "time": "2016-11-11T18:43:20+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "mopa/bootstrap-bundle", "version": "v2.3.1", "target-dir": "Mopa/Bundle/BootstrapBundle", "source": {"type": "git", "url": "https://github.com/phiamo/MopaBootstrapBundle.git", "reference": "82523811144266910868d51f5396d110338d7fea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phiamo/MopaBootstrapBundle/zipball/82523811144266910868d51f5396d110338d7fea", "reference": "82523811144266910868d51f5396d110338d7fea", "shasum": ""}, "require": {"mopa/composer-bridge": "1.3.*", "symfony/symfony": "~2.3"}, "suggest": {"craue/formflow-bundle": "dev-master", "jlong/sass-twitter-bootstrap": "dev-master", "knplabs/knp-menu-bundle": "2.0.*", "knplabs/knp-paginator-bundle": "dev-master", "mopa/bootstrap-sandbox-bundle": "~2.3", "twbs/bootstrap": "2.*"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-0": {"Mopa\\Bundle\\BootstrapBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Community contributions", "homepage": "https://github.com/phiamo/MopaBootstrapBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Easy integration of twitters bootstrap into symfony2", "keywords": ["bootstrap", "bootstrap form", "extra form", "form", "template"], "support": {"issues": "https://github.com/phiamo/MopaBootstrapBundle/issues", "source": "https://github.com/phiamo/MopaBootstrapBundle/tree/v2.3.x"}, "time": "2014-08-26T22:53:53+00:00"}, {"name": "mopa/composer-bridge", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/phiamo/MopaComposerBridge.git", "reference": "bbbd9b4595993cda248a996f21af3298f12fa331"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phiamo/MopaComposerBridge/zipball/bbbd9b4595993cda248a996f21af3298f12fa331", "reference": "bbbd9b4595993cda248a996f21af3298f12fa331", "shasum": ""}, "require": {"symfony/console": ">=2.0.0"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mopa\\Bridge\\Composer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Community contributions", "homepage": "https://github.com/phiamo/MopaComposerBridge/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony2 Composer Bridge", "homepage": "http://bootstrap.mohrenweiserpartner.de", "keywords": ["Symfony2", "composer"], "support": {"issues": "https://github.com/phiamo/MopaComposerBridge/issues", "source": "https://github.com/phiamo/MopaComposerBridge/tree/master"}, "time": "2014-12-29T13:24:12+00:00"}, {"name": "paragonie/random_compat", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "9b3899e3c3ddde89016f576edb8c489708ad64cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/9b3899e3c3ddde89016f576edb8c489708ad64cd", "reference": "9b3899e3c3ddde89016f576edb8c489708ad64cd", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2018-04-04T21:48:54+00:00"}, {"name": "phpcollection/phpcollection", "version": "0.6.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-collection.git", "reference": "56d18c8c2c0400f2838703246ac7de919a605763"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-collection/zipball/56d18c8c2c0400f2838703246ac7de919a605763", "reference": "56d18c8c2c0400f2838703246ac7de919a605763", "shasum": ""}, "require": {"phpoption/phpoption": "1.*"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.4-dev"}}, "autoload": {"psr-0": {"PhpCollection": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "General-Purpose Collection Library for PHP", "keywords": ["collection", "list", "map", "sequence", "set"], "support": {"issues": "https://github.com/schmittjoh/php-collection/issues", "source": "https://github.com/schmittjoh/php-collection/tree/0.6.0"}, "time": "2022-03-21T13:02:41+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "playbloom/guzzle-bundle", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/ludofleury/GuzzleBundle.git", "reference": "d8c97d9f9680ad38ecef0ceb39734278d746a9ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ludofleury/GuzzleBundle/zipball/d8c97d9f9680ad38ecef0ceb39734278d746a9ed", "reference": "d8c97d9f9680ad38ecef0ceb39734278d746a9ed", "shasum": ""}, "require": {"guzzle/guzzle": "3.*", "php": ">=5.6", "symfony/config": "~2.1|~3.0|^4.0", "symfony/dependency-injection": "~2.1|~3.0|^4.0", "symfony/http-foundation": "~2.1|~3.0|^4.0", "symfony/http-kernel": "~2.1|~3.0|^4.0", "symfony/web-profiler-bundle": "~2.1|~3.0|^4.0", "symfony/yaml": "~2.1|~3.0|^4.0"}, "require-dev": {"phpunit/phpunit": "~5.7", "squizlabs/php_codesniffer": "^3.12"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Playbloom\\Bundle\\GuzzleBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Provide Symfony2 web profiler for Guzzle", "homepage": "https://github.com/ludofleury/GuzzleBundle", "keywords": ["Guzzle", "Symfony2", "profiler"], "support": {"issues": "https://github.com/ludofleury/GuzzleBundle/issues", "source": "https://github.com/ludofleury/GuzzleBundle/tree/v1.3.0"}, "time": "2025-05-01T11:11:37+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "samj/doctrine-sluggable-bundle", "version": "v2.0", "source": {"type": "git", "url": "https://github.com/samjarrett/DoctrineSluggableBundle.git", "reference": "9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/samjarrett/DoctrineSluggableBundle/zipball/9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23", "reference": "9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"SamJ": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "cameron<PERSON><PERSON>@gmail.com", "role": "Original Author"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/samjarrett/DoctrineSluggableBundle/issues", "source": "https://github.com/samjarrett/DoctrineSluggableBundle/tree/master"}, "time": "2014-01-02T00:06:43+00:00"}, {"name": "sensio/distribution-bundle", "version": "v4.0.42", "target-dir": "Sensio/Bundle/DistributionBundle", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioDistributionBundle.git", "reference": "71fdc7d57538f17eedf3c548c91ab214c694a9c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioDistributionBundle/zipball/71fdc7d57538f17eedf3c548c91ab214c694a9c0", "reference": "71fdc7d57538f17eedf3c548c91ab214c694a9c0", "shasum": ""}, "require": {"php": ">=5.3.9", "sensiolabs/security-checker": "~3.0|~4.0|~5.0|~6.0", "symfony/class-loader": "~2.2", "symfony/framework-bundle": "~2.3", "symfony/process": "~2.2"}, "require-dev": {"symfony/form": "~2.2", "symfony/validator": "~2.2", "symfony/yaml": "~2.2"}, "suggest": {"symfony/form": "If you want to use the configurator", "symfony/validator": "If you want to use the configurator", "symfony/yaml": "If you want to use  the configurator"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-0": {"Sensio\\Bundle\\DistributionBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Base bundle for Symfony Distributions", "keywords": ["configuration", "distribution"], "support": {"issues": "https://github.com/sensiolabs/SensioDistributionBundle/issues", "source": "https://github.com/sensiolabs/SensioDistributionBundle/tree/4.0"}, "abandoned": true, "time": "2019-06-18T15:41:34+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v3.0.29", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "bb907234df776b68922eb4b25bfa061683597b6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/bb907234df776b68922eb4b25bfa061683597b6a", "reference": "bb907234df776b68922eb4b25bfa061683597b6a", "shasum": ""}, "require": {"doctrine/common": "~2.2", "symfony/dependency-injection": "~2.3|~3.0", "symfony/framework-bundle": "~2.3|~3.0|~4.0"}, "require-dev": {"doctrine/doctrine-bundle": "~1.5", "doctrine/orm": "~2.4,>=2.4.5", "symfony/asset": "~2.7|~3.0|~4.0", "symfony/browser-kit": "~2.3|~3.0|~4.0", "symfony/dom-crawler": "~2.3|~3.0|~4.0", "symfony/expression-language": "~2.4|~3.0|~4.0", "symfony/finder": "~2.3|~3.0|~4.0", "symfony/phpunit-bridge": "~3.2|~4.0", "symfony/psr-http-message-bridge": "^0.3|^1.0", "symfony/security-bundle": "~2.4|~3.0|~4.0", "symfony/templating": "~2.3|~3.0|~4.0", "symfony/translation": "~2.3|~3.0|~4.0", "symfony/twig-bundle": "~2.3|~3.0|~4.0", "symfony/yaml": "~2.3|~3.0|~4.0", "twig/twig": "~1.12|~2.0", "zendframework/zend-diactoros": "^1.3"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/3.0"}, "abandoned": "Symfony", "time": "2017-12-14T19:03:23+00:00"}, {"name": "sensiolabs/security-checker", "version": "v5.0.3", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "46be3f58adac13084497961e10eed9a7fb4d44d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/46be3f58adac13084497961e10eed9a7fb4d44d1", "reference": "46be3f58adac13084497961e10eed9a7fb4d44d1", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "php": ">=5.5.9", "symfony/console": "~2.7|~3.0|~4.0"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"SensioLabs\\Security\\": "SensioLabs/Security"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "support": {"issues": "https://github.com/sensiolabs/security-checker/issues", "source": "https://github.com/sensiolabs/security-checker/tree/master"}, "abandoned": "https://github.com/fabpot/local-php-security-checker", "time": "2018-12-19T17:14:59+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "181b89f18a90f8925ef805f950d47a7190e9b950"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/181b89f18a90f8925ef805f950d47a7190e9b950", "reference": "181b89f18a90f8925ef805f950d47a7190e9b950", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v5.4.12"}, "abandoned": "symfony/mailer", "time": "2018-07-31T09:26:32+00:00"}, {"name": "symfony/assetic-bundle", "version": "v2.8.2", "source": {"type": "git", "url": "https://github.com/symfony/assetic-bundle.git", "reference": "2e0a23a4874838e26de6f025e02fc63328921a4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/assetic-bundle/zipball/2e0a23a4874838e26de6f025e02fc63328921a4c", "reference": "2e0a23a4874838e26de6f025e02fc63328921a4c", "shasum": ""}, "require": {"kriswallsmith/assetic": "~1.4", "php": ">=5.3.0", "symfony/console": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/framework-bundle": "~2.3|~3.0", "symfony/yaml": "~2.3|~3.0"}, "conflict": {"kriswallsmith/spork": "<=0.2", "twig/twig": "<1.27"}, "require-dev": {"kriswallsmith/spork": "~0.3", "patchwork/jsqueeze": "~1.0", "symfony/class-loader": "~2.3|~3.0", "symfony/css-selector": "~2.3|~3.0", "symfony/dom-crawler": "~2.3|~3.0", "symfony/phpunit-bridge": "~2.7|~3.0", "symfony/twig-bundle": "~2.3|~3.0"}, "suggest": {"kriswallsmith/spork": "to be able to dump assets in parallel", "symfony/twig-bundle": "to use the Twig integration"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\AsseticBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Integrates Assetic into Symfony2", "homepage": "https://github.com/symfony/AsseticBundle", "keywords": ["assets", "compression", "minification"], "support": {"issues": "https://github.com/symfony/assetic-bundle/issues", "source": "https://github.com/symfony/assetic-bundle/tree/master"}, "abandoned": "symfony/webpack-encore-pack", "time": "2017-07-14T07:26:46+00:00"}, {"name": "symfony/monolog-bundle", "version": "v2.12.1", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "b0146bdca7ba2a65f3bbe7010423c7393b29ec3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/b0146bdca7ba2a65f3bbe7010423c7393b29ec3f", "reference": "b0146bdca7ba2a65f3bbe7010423c7393b29ec3f", "shasum": ""}, "require": {"monolog/monolog": "~1.18", "php": ">=5.3.2", "symfony/config": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/http-kernel": "~2.3|~3.0", "symfony/monolog-bridge": "~2.3|~3.0"}, "require-dev": {"phpunit/phpunit": "^4.8", "symfony/console": "~2.3|~3.0", "symfony/yaml": "~2.3|~3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/2.x"}, "time": "2017-01-02T19:04:26+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "9cba05c714911d416f478a74d1758865f7373c3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/9cba05c714911d416f478a74d1758865f7373c3f", "reference": "9cba05c714911d416f478a74d1758865f7373c3f", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-apcu/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v2.6.7", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "c4808f5169efc05567be983909d00f00521c53ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/c4808f5169efc05567be983909d00f00521c53ec", "reference": "c4808f5169efc05567be983909d00f00521c53ec", "shasum": ""}, "require": {"php": ">=5.3.2", "swiftmailer/swiftmailer": "~4.2|~5.0", "symfony/config": "~2.7|~3.0", "symfony/dependency-injection": "~2.7|~3.0", "symfony/http-kernel": "~2.7|~3.0"}, "require-dev": {"symfony/console": "~2.7|~3.0", "symfony/framework-bundle": "~2.7|~3.0", "symfony/phpunit-bridge": "~3.3@dev", "symfony/yaml": "~2.7|~3.0"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/2.6"}, "abandoned": "symfony/mailer", "time": "2017-10-19T01:06:41+00:00"}, {"name": "symfony/symfony", "version": "v2.7.52", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "4d0899c5e3b30b3be591f8349ce92529d79bee93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/4d0899c5e3b30b3be591f8349ce92529d79bee93", "reference": "4d0899c5e3b30b3be591f8349ce92529d79bee93", "shasum": ""}, "require": {"doctrine/common": "~2.4", "ext-xml": "*", "paragonie/random_compat": "~1.0", "php": ">=5.3.9", "psr/log": "~1.0", "symfony/polyfill-apcu": "~1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.1", "twig/twig": "~1.34|~2.4"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "replace": {"symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/class-loader": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/intl": "self.version", "symfony/locale": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-acl": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-http": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/swiftmailer-bridge": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.4", "doctrine/doctrine-bundle": "~1.2", "doctrine/orm": "~2.4,>=2.4.5", "egulias/email-validator": "~1.2,>=1.2.1", "ircmaxell/password-compat": "~1.0", "monolog/monolog": "~1.11", "ocramius/proxy-manager": "~0.4|~1.0|~2.0", "sensio/framework-extra-bundle": "^3.0.2", "symfony/phpunit-bridge": "~3.4|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"files": ["src/Symfony/Component/Intl/Resources/stubs/functions.php"], "psr-4": {"Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\Swiftmailer\\": "src/Symfony/Bridge/Swiftmailer/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/"}, "classmap": ["src/Symfony/Component/HttpFoundation/Resources/stubs", "src/Symfony/Component/Intl/Resources/stubs"], "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "support": {"issues": "https://github.com/symfony/symfony/issues", "source": "https://github.com/symfony/symfony/tree/v2.7.52"}, "time": "2019-04-17T16:37:53+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "support": {"issues": "https://github.com/twigphp/Twig-extensions/issues", "source": "https://github.com/twigphp/Twig-extensions/tree/master"}, "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.16.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/19185947ec75d433a3ac650af32fc05649b95ee1", "reference": "19185947ec75d433a3ac650af32fc05649b95ee1", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^5.4.9|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.16-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.16.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-09-09T17:53:56+00:00"}, {"name": "vich/uploader-bundle", "version": "v0.14.0", "source": {"type": "git", "url": "https://github.com/dustin10/VichUploaderBundle.git", "reference": "65d244d5e6ef62b4978585cdeb283b6cdab05f6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dustin10/VichUploaderBundle/zipball/65d244d5e6ef62b4978585cdeb283b6cdab05f6b", "reference": "65d244d5e6ef62b4978585cdeb283b6cdab05f6b", "shasum": ""}, "require": {"jms/metadata": "~1.5", "php": ">=5.3.2", "symfony/finder": ">=2.0", "symfony/framework-bundle": "~2.3", "symfony/property-access": "~2.3"}, "require-dev": {"doctrine/doctrine-bundle": "*", "doctrine/mongodb-odm": "@dev", "doctrine/orm": "*", "knplabs/knp-gaufrette-bundle": "*", "matthiasnoback/symfony-dependency-injection-test": "0.*", "mikey179/vfsstream": "~1.0", "oneup/flysystem-bundle": "dev-master", "phpunit/phpunit": "~4.0", "symfony/symfony": "*"}, "suggest": {"doctrine/doctrine-bundle": "*", "doctrine/mongodb-odm-bundle": "*", "doctrine/orm": ">=2.2.3", "doctrine/phpcr-odm": "~1.0", "knplabs/knp-gaufrette-bundle": "*", "symfony/yaml": "@stable", "willdurand/propel-eventdispatcher-bundle": ">=1.2"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Vich\\TestBundle\\": "Tests/Fixtures/App/src/TestBundle/", "Vich\\UploaderBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "d<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Ease file uploads attached to entities", "homepage": "https://github.com/dustin10/VichUploaderBundle", "keywords": ["file uploads", "upload"], "support": {"issues": "https://github.com/dustin10/VichUploaderBundle/issues", "source": "https://github.com/dustin10/VichUploaderBundle/tree/master"}, "time": "2014-12-12T10:26:46+00:00"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "support": {"issues": "https://github.com/willdurand/JsonpCallbackValidator/issues", "source": "https://github.com/willdurand/JsonpCallbackValidator/tree/master"}, "time": "2014-01-20T22:35:06+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "2a59f2376557303e3fa91465ab691abb82945edf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/2a59f2376557303e3fa91465ab691abb82945edf", "reference": "2a59f2376557303e3fa91465ab691abb82945edf", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "support": {"issues": "https://github.com/willdurand/Negotiation/issues", "source": "https://github.com/willdurand/Negotiation/tree/1.5.0"}, "time": "2015-10-01T07:42:40+00:00"}], "packages-dev": [{"name": "sensio/generator-bundle", "version": "v2.5.3", "target-dir": "Sensio/Bundle/GeneratorBundle", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioGeneratorBundle.git", "reference": "e50108c2133ee5c9c484555faed50c17a61221d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioGeneratorBundle/zipball/e50108c2133ee5c9c484555faed50c17a61221d3", "reference": "e50108c2133ee5c9c484555faed50c17a61221d3", "shasum": ""}, "require": {"symfony/console": "~2.5", "symfony/framework-bundle": "~2.2"}, "require-dev": {"doctrine/orm": "~2.2,>=2.2.3", "symfony/doctrine-bridge": "~2.2", "twig/twig": "~1.11"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "autoload": {"psr-0": {"Sensio\\Bundle\\GeneratorBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle generates code for you", "support": {"issues": "https://github.com/sensiolabs/SensioGeneratorBundle/issues", "source": "https://github.com/sensiolabs/SensioGeneratorBundle/tree/master"}, "abandoned": "symfony/maker-bundle", "time": "2015-03-17T06:36:52+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"fredcido/phpsvg": 20, "friendsofsymfony/jsrouting-bundle": 0, "glanchow/doctrine-fuzzy": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.3.9"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}